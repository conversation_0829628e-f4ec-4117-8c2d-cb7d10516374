# 花火邮箱助手部署指南

本文档提供了花火邮箱助手项目的详细部署说明，支持多种部署方式，包括Docker部署、Docker Compose部署和源代码部署。

## 目录

- [系统要求](#系统要求)
- [Docker部署](#docker部署)
- [Docker Compose部署](#docker-compose部署)
- [源代码部署](#源代码部署)
- [Nginx反向代理配置](#nginx反向代理配置)
- [SSL配置](#ssl配置)
- [环境变量配置](#环境变量配置)
- [数据持久化](#数据持久化)
- [更新升级](#更新升级)
- [常见问题](#常见问题)

## 系统要求

### Docker部署

- Docker 19.03+
- 至少1GB RAM
- 至少10GB磁盘空间

### 源代码部署

- Python 3.10+
- Node.js 16+
- npm 8+
- 至少1GB RAM
- 至少5GB磁盘空间

## Docker部署

Docker部署是最简单的部署方式，只需一条命令即可完成部署。

### 基本部署

```bash
# 拉取镜像
docker pull luofengyuan/firemail:latest

# 运行容器
docker run -d \
  --name firemail \
  -p 80:80 \
  -v ./data:/app/backend/data \
  --restart unless-stopped \
  luofengyuan/firemail:latest
```

### 自定义端口

如果要使用自定义端口，可以这样配置：

```bash
docker run -d \
  --name firemail \
  -p 8080:80 \
  -v ./data:/app/backend/data \
  --restart unless-stopped \
  luofengyuan/firemail:latest
```

这将使应用在主机的8080端口上可访问。

### 环境变量配置

```bash
docker run -d \
  --name firemail \
  -p 80:80 \
  -v ./data:/app/backend/data \
  -e JWT_SECRET_KEY=your_secret_key_here \
  -e TZ=Asia/Shanghai \
  --restart unless-stopped \
  luofengyuan/firemail:latest
```

## Docker Compose部署

Docker Compose部署适合需要更多自定义配置的场景。

### 创建配置文件

创建`docker-compose.yml`文件：

```yaml
version: '3'

services:
  FireMail:
    image: luofengyuan/firemail:latest
    container_name: firemail
    restart: unless-stopped
    ports:
      - "80:80"  # 只暴露一个端口，通过Nginx进行反向代理
    volumes:
      - ./data:/app/backend/data
    environment:
      - TZ=Asia/Shanghai
      - HOST=0.0.0.0
      - FLASK_PORT=5000  # 后端服务器端口
      - WS_PORT=8765     # WebSocket服务器端口
      - JWT_SECRET_KEY=your_secret_key_here  # 建议修改为随机字符串
```

### 启动服务

```bash
docker-compose up -d
```

### 查看日志

```bash
docker-compose logs -f
```

### 停止服务

```bash
docker-compose down
```

## 源代码部署

源代码部署适合开发环境或需要定制化的场景。

### 克隆代码

```bash
git clone https://github.com/fengyuanluo/firemail.git
cd firemail
```

### 安装后端依赖

```bash
pip install -r requirements.txt
```

### 安装前端依赖并构建

```bash
cd frontend
npm install
npm run build
```

### 前端开发环境配置

如果需要在开发环境中运行前端，可以使用以下命令：

```bash
# 使用默认端口(3000)启动
npm run dev

# 使用自定义端口启动
# Linux/Mac
VITE_PORT=3001 npm run dev

# Windows
set VITE_PORT=3001
npm run dev

# 使用带端口提示的脚本(会显示当前使用的端口)
npm run dev:port
```

### 配置环境变量

```bash
# Linux/Mac
export JWT_SECRET_KEY=your_secret_key_here
export FLASK_PORT=5000
export WS_PORT=8765

# Windows
set JWT_SECRET_KEY=your_secret_key_here
set FLASK_PORT=5000
set WS_PORT=8765
```

### 启动后端服务

```bash
cd ../backend
python app.py
```

### 使用生产服务器（可选）

如果需要在生产环境中运行，建议使用Gunicorn：

```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

## Nginx反向代理配置

如果您需要通过Nginx提供服务，可以使用以下配置：

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /ws/ {
        proxy_pass http://localhost:8765;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_read_timeout 86400;
    }
}
```

## SSL配置

### 自签名证书（测试环境）

```bash
# 生成自签名证书
openssl req -x509 -nodes -days 365 -newkey rsa:2048 -keyout firemail.key -out firemail.crt
```

### Nginx SSL配置

```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;

    ssl_certificate /path/to/firemail.crt;
    ssl_certificate_key /path/to/firemail.key;

    # SSL配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers 'EECDH+AESGCM:EDH+AESGCM:AES256+EECDH:AES256+EDH';
    ssl_session_cache shared:SSL:10m;

    location / {
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /ws/ {
        proxy_pass http://localhost:8765;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_read_timeout 86400;
    }
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$host$request_uri;
}
```

### Docker Compose与Let's Encrypt集成

如果需要自动配置Let's Encrypt证书，可以使用以下docker-compose配置：

```yaml
version: '3'

services:
  FireMail:
    image: luofengyuan/firemail:latest
    restart: unless-stopped
    volumes:
      - ./data:/app/backend/data
    environment:
      - TZ=Asia/Shanghai
      - JWT_SECRET_KEY=your_secret_key_here
    networks:
      - web

  caddy:
    image: caddy:2
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./Caddyfile:/etc/caddy/Caddyfile
      - caddy_data:/data
      - caddy_config:/config
    networks:
      - web

networks:
  web:
    driver: bridge

volumes:
  caddy_data:
  caddy_config:
```

创建`Caddyfile`：

```
your-domain.com {
    reverse_proxy /* FireMail:5000
    reverse_proxy /ws/* FireMail:8765 {
        header_up Host {host}
        header_up X-Real-IP {remote}
        header_up X-Forwarded-For {remote}
        header_up X-Forwarded-Proto {scheme}
        websocket
    }
}
```

## 环境变量配置

花火邮箱助手支持以下环境变量配置：

| 环境变量 | 说明 | 默认值 |
|---------|------|-------|
| JWT_SECRET_KEY | JWT签名密钥 | huohuo_email_secret_key |
| HOST | 主机地址 | 0.0.0.0 |
| FLASK_PORT | Flask服务器端口 | 5000 |
| WS_PORT | WebSocket服务器端口 | 8765 |
| FRONTEND_PORT | 前端开发服务器端口 | 3000 |
| VITE_PORT | 前端开发服务器端口(开发环境) | 3000 |
| TZ | 时区 | Asia/Shanghai |

## 数据持久化

### Docker数据卷

使用Docker部署时，数据存储在`/app/backend/data`目录中。为了持久化数据，需要将此目录映射到主机目录：

```bash
docker run -d \
  --name firemail \
  -p 80:80 \
  -v /path/to/data:/app/backend/data \
  luofengyuan/firemail:latest
```

### 数据备份

建议定期备份数据库文件，可以使用以下脚本：

```bash
#!/bin/bash
BACKUP_DIR="/path/to/backups"
DATE=$(date +%Y%m%d)
CONTAINER_NAME="firemail"

# 创建备份目录
mkdir -p "$BACKUP_DIR"

# 备份数据库
docker cp $CONTAINER_NAME:/app/backend/data/huohuo_email.db "$BACKUP_DIR/huohuo_email_$DATE.db"

# 保留最近30天的备份
find "$BACKUP_DIR" -name "huohuo_email_*.db" -type f -mtime +30 -delete
```

将此脚本添加到crontab中定期执行：

```
0 2 * * * /path/to/backup.sh
```

## 更新升级

### Docker更新

```bash
# 拉取最新镜像
docker pull luofengyuan/firemail:latest

# 停止并删除旧容器
docker stop firemail
docker rm firemail

# 创建新容器
docker run -d \
  --name firemail \
  -p 80:80 \
  -v /path/to/data:/app/backend/data \
  --restart unless-stopped \
  luofengyuan/firemail:latest
```

### Docker Compose更新

```bash
# 拉取最新镜像
docker-compose pull

# 重启服务
docker-compose up -d
```

### 源代码更新

```bash
# 更新代码
git pull

# 更新后端依赖
pip install -r requirements.txt

# 更新前端依赖并重新构建
cd frontend
npm install
npm run build

# 重启服务
cd ../backend
python app.py
```

## 常见问题

### 1. 数据库迁移

如果需要将数据从一个实例迁移到另一个实例，可以直接复制数据库文件：

```bash
# 停止源容器
docker stop firemail_source

# 拷贝数据库文件
docker cp firemail_source:/app/backend/data/huohuo_email.db /tmp/

# 停止目标容器
docker stop firemail_target

# 拷贝到目标容器
docker cp /tmp/huohuo_email.db firemail_target:/app/backend/data/

# 启动容器
docker start firemail_source
docker start firemail_target
```

### 2. WebSocket连接问题

如果遇到WebSocket连接问题，请检查：

1. 防火墙是否开放了WebSocket端口（默认8765）
2. 是否正确配置了反向代理（如Nginx配置）
3. 客户端是否使用了正确的WebSocket地址

示例WebSocket地址：`ws://your-domain.com/ws/`

### 3. 权限问题

当使用Docker部署时，如果遇到权限问题，可以通过修改数据卷权限解决：

```bash
# 找到容器中的用户ID
docker exec firemail id

# 修改主机上的文件权限
sudo chown -R 1000:1000 /path/to/data
```

### 4. 内存使用优化

如果在低内存环境中运行，可以限制Docker容器的内存使用：

```bash
docker run -d \
  --name firemail \
  -p 80:80 \
  -v /path/to/data:/app/backend/data \
  --memory="512m" \
  --memory-swap="1g" \
  luofengyuan/firemail:latest
```

### 5. 日志管理

默认情况下，Docker容器的日志会不断增长。建议配置日志轮转：

```bash
# 创建/etc/docker/daemon.json文件
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  }
}

# 重启Docker服务
sudo systemctl restart docker
```

### 6. 重置管理员密码

如果忘记了管理员密码，可以通过以下步骤重置：

```bash
# 进入容器
docker exec -it firemail bash

# 进入Python环境
cd /app/backend
python

# 执行以下Python代码
from database.db import Database
db = Database()
db.update_user_password(1, "new_password")  # 1通常是第一个管理员账号的ID
```