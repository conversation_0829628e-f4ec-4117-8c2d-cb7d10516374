<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8">
    <link rel="icon" href="/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>花火邮箱助手</title>
    <!-- 环境配置 -->
    <!-- 注意: env-config.js文件可能不存在，这会导致API请求失败 -->
    <script>
      // 本地调试配置，防止env-config.js不存在的情况
      // 使用完整URL直接连接后端服务
      const hostname = window.location.hostname;
      // 注意：不要在URL末尾添加/api，因为api.js中会自动添加
      window.API_URL = `http://${hostname}:5000`;
      window.WS_URL = `ws://${hostname}:8765`;

      // 设置SameSite和Partitioned属性的Cookie策略
      document.cookie = "CookieConsent=true; SameSite=None; Secure; Partitioned;";

      console.log('使用直接连接配置: API_URL =', window.API_URL, 'WS_URL =', window.WS_URL);
    </script>
    <script src="/env-config.js"></script>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>