<template>
  <div class="email-status-indicator">
    <!-- 空闲状态 -->
    <div v-if="status === 'idle'" class="status-item status-idle">
      <div class="status-dot idle"></div>
      <span class="status-text">空闲</span>
    </div>

    <!-- 处理中状态 -->
    <div v-else-if="status === 'processing'" class="status-item status-processing">
      <div class="status-dot processing"></div>
      <span class="status-text">检查中</span>
      <div class="pulse-ring"></div>
    </div>

    <!-- 完成状态 -->
    <div v-else-if="status === 'completed'" class="status-item status-completed">
      <div class="status-dot completed"></div>
      <span class="status-text">已完成</span>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="status === 'error'" class="status-item status-error">
      <div class="status-dot error"></div>
      <span class="status-text">错误</span>
      <el-tooltip v-if="errorMessage" :content="errorMessage" placement="top">
        <el-icon class="error-info"><Warning /></el-icon>
      </el-tooltip>
    </div>

    <!-- 连接中状态 -->
    <div v-else-if="status === 'connecting'" class="status-item status-connecting">
      <div class="status-dot connecting"></div>
      <span class="status-text">连接中</span>
    </div>

    <!-- 未知状态 -->
    <div v-else class="status-item status-unknown">
      <div class="status-dot unknown"></div>
      <span class="status-text">未知</span>
    </div>

    <!-- 最后活动时间 -->
    <div v-if="showLastActivity && lastActivity" class="last-activity">
      <el-text size="small" type="info">
        {{ formatLastActivity(lastActivity) }}
      </el-text>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { Warning } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'

dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

const props = defineProps({
  // 状态: idle, processing, completed, error, connecting
  status: {
    type: String,
    default: 'idle',
    validator: (value) => ['idle', 'processing', 'completed', 'error', 'connecting'].includes(value)
  },
  // 错误消息
  errorMessage: {
    type: String,
    default: ''
  },
  // 最后活动时间
  lastActivity: {
    type: String,
    default: ''
  },
  // 是否显示最后活动时间
  showLastActivity: {
    type: Boolean,
    default: false
  },
  // 大小: small, medium, large
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  }
})

// 格式化最后活动时间
const formatLastActivity = (timeString) => {
  if (!timeString) return ''
  
  const now = dayjs()
  const time = dayjs(timeString)
  const diffInMinutes = now.diff(time, 'minute')
  
  if (diffInMinutes < 1) {
    return '刚刚'
  } else if (diffInMinutes < 60) {
    return `${diffInMinutes}分钟前`
  } else if (diffInMinutes < 1440) { // 24小时
    return `${Math.floor(diffInMinutes / 60)}小时前`
  } else {
    return time.format('MM-DD HH:mm')
  }
}
</script>

<style scoped>
.email-status-indicator {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 6px;
  position: relative;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  position: relative;
  flex-shrink: 0;
}

.status-text {
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.error-info {
  color: var(--el-color-danger);
  font-size: 14px;
  cursor: pointer;
}

.last-activity {
  margin-left: 14px;
  font-size: 10px;
  opacity: 0.7;
}

/* 状态颜色 */
.status-idle .status-dot {
  background-color: #909399;
}

.status-idle .status-text {
  color: #909399;
}

.status-processing .status-dot {
  background-color: #E6A23C;
  animation: pulse 2s infinite;
}

.status-processing .status-text {
  color: #E6A23C;
}

.status-completed .status-dot {
  background-color: #67C23A;
}

.status-completed .status-text {
  color: #67C23A;
}

.status-error .status-dot {
  background-color: #F56C6C;
}

.status-error .status-text {
  color: #F56C6C;
}

.status-connecting .status-dot {
  background-color: #409EFF;
  animation: pulse 1.5s infinite;
}

.status-connecting .status-text {
  color: #409EFF;
}

.status-unknown .status-dot {
  background-color: #C0C4CC;
}

.status-unknown .status-text {
  color: #C0C4CC;
}

/* 脉冲动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 处理中状态的脉冲环 */
.pulse-ring {
  position: absolute;
  left: -2px;
  top: -2px;
  width: 12px;
  height: 12px;
  border: 2px solid #E6A23C;
  border-radius: 50%;
  opacity: 0;
  animation: pulse-ring 2s infinite;
}

@keyframes pulse-ring {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* 大小变体 */
.email-status-indicator.small .status-dot {
  width: 6px;
  height: 6px;
}

.email-status-indicator.small .status-text {
  font-size: 10px;
}

.email-status-indicator.small .pulse-ring {
  width: 10px;
  height: 10px;
  left: -2px;
  top: -2px;
}

.email-status-indicator.large .status-dot {
  width: 10px;
  height: 10px;
}

.email-status-indicator.large .status-text {
  font-size: 14px;
}

.email-status-indicator.large .pulse-ring {
  width: 14px;
  height: 14px;
  left: -2px;
  top: -2px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .status-text {
    font-size: 11px;
  }
  
  .last-activity {
    font-size: 9px;
  }
}
</style>
