<template>
  <div class="email-check-progress">
    <!-- 错误状态 - 最高优先级 -->
    <div v-if="hasError" class="status-error">
      <el-tag type="danger" size="small" effect="plain">
        <el-icon><Warning /></el-icon>
        错误
      </el-tag>
      <el-tooltip v-if="errorMessage" :content="errorMessage" placement="top">
        <el-icon class="error-icon"><InfoFilled /></el-icon>
      </el-tooltip>
    </div>

    <!-- 完成状态 - 第二优先级 -->
    <div v-else-if="isCompleted || progress === 100" class="status-completed">
      <div class="completion-header">
        <el-tag type="success" size="small" effect="plain">
          <el-icon><Check /></el-icon>
          已完成
        </el-tag>
        <span class="completion-percentage">100%</span>
      </div>

      <!-- 完成进度条 -->
      <el-progress
        :percentage="100"
        :stroke-width="6"
        :show-text="false"
        color="#67C23A"
        class="progress-bar"
      />

      <div class="completion-info">
        <el-text size="small" type="success" v-if="message">
          {{ message }}
        </el-text>
        <el-text size="small" type="info" v-if="completedTime">
          {{ formatDate(completedTime) }}
        </el-text>
      </div>
    </div>

    <!-- 检查中状态 - 第三优先级 -->
    <div v-else-if="isProcessing" class="status-processing">
      <div class="progress-header">
        <el-tag type="warning" size="small" effect="plain">
          <el-icon class="rotating"><Loading /></el-icon>
          检查中
        </el-tag>
        <span class="progress-percentage">{{ progress }}%</span>
      </div>

      <!-- 进度条 -->
      <el-progress
        :percentage="progress"
        :stroke-width="6"
        :show-text="false"
        :color="progressColor"
        class="progress-bar"
      />

      <!-- 状态消息 -->
      <div class="progress-message" v-if="message">
        <el-text size="small" type="info">{{ message }}</el-text>
      </div>

      <!-- 时间信息 -->
      <div class="time-info" v-if="showTimeInfo">
        <div class="time-item">
          <el-text size="small" type="info">
            <el-icon><Timer /></el-icon>
            已用时: {{ formatDuration(elapsedTime) }}
          </el-text>
        </div>
        <div class="time-item" v-if="estimatedTimeRemaining > 0">
          <el-text size="small" type="info">
            <el-icon><Clock /></el-icon>
            预计剩余: {{ formatDuration(estimatedTimeRemaining) }}
          </el-text>
        </div>
      </div>
    </div>

    <!-- 空闲状态 - 默认状态 -->
    <div v-else class="status-idle">
      <el-tag type="info" size="small" effect="plain">
        <el-icon><Clock /></el-icon>
        空闲
      </el-tag>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { 
  Clock, 
  Loading, 
  Timer, 
  Warning, 
  Check, 
  InfoFilled 
} from '@element-plus/icons-vue'
import dayjs from 'dayjs'

const props = defineProps({
  // 邮箱ID
  emailId: {
    type: [String, Number],
    required: true
  },
  // 进度百分比 (0-100)
  progress: {
    type: Number,
    default: 0
  },
  // 状态消息
  message: {
    type: String,
    default: ''
  },
  // 是否正在处理中
  isProcessing: {
    type: Boolean,
    default: false
  },
  // 是否有错误
  hasError: {
    type: Boolean,
    default: false
  },
  // 错误消息
  errorMessage: {
    type: String,
    default: ''
  },
  // 是否已完成
  isCompleted: {
    type: Boolean,
    default: false
  },
  // 完成时间
  completedTime: {
    type: String,
    default: ''
  },
  // 是否显示时间信息
  showTimeInfo: {
    type: Boolean,
    default: true
  },
  // 开始时间
  startTime: {
    type: String,
    default: ''
  }
})

// 响应式数据
const elapsedTime = ref(0)
const estimatedTimeRemaining = ref(0)
const timer = ref(null)

// 计算属性
const progressColor = computed(() => {
  if (props.progress < 30) return '#E6A23C'
  if (props.progress < 70) return '#409EFF'
  return '#67C23A'
})

// 方法定义（需要在watch之前定义）
const startTimer = () => {
  if (timer.value) return

  const startTimestamp = props.startTime ? new Date(props.startTime).getTime() : Date.now()

  timer.value = setInterval(() => {
    elapsedTime.value = Math.floor((Date.now() - startTimestamp) / 1000)
  }, 1000)
}

const stopTimer = () => {
  if (timer.value) {
    clearInterval(timer.value)
    timer.value = null
  }
}

// 监听处理状态变化
watch(() => props.isProcessing, (newVal) => {
  if (newVal) {
    startTimer()
  } else {
    stopTimer()
  }
}, { immediate: true })

// 监听进度变化，计算预估时间
watch(() => props.progress, (newProgress) => {
  if (newProgress > 0 && elapsedTime.value > 0) {
    // 基于当前进度和已用时间估算剩余时间
    const timePerPercent = elapsedTime.value / newProgress
    estimatedTimeRemaining.value = Math.max(0, (100 - newProgress) * timePerPercent)
  }
})

const formatDuration = (seconds) => {
  if (seconds < 60) {
    return `${seconds}秒`
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}分${remainingSeconds}秒`
  } else {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return `${hours}小时${minutes}分钟`
  }
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  return dayjs(dateString).format('HH:mm:ss')
}

// 生命周期
onMounted(() => {
  if (props.isProcessing) {
    startTimer()
  }
})

onUnmounted(() => {
  stopTimer()
})
</script>

<style scoped>
.email-check-progress {
  min-width: 200px;
  padding: 8px;
}

.status-idle,
.status-error,
.status-completed {
  display: flex;
  align-items: center;
  gap: 8px;
}

.completion-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  margin-top: 4px;
}

.completion-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.completion-percentage {
  font-weight: 600;
  color: #67C23A;
  font-size: 12px;
}

.status-processing {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.progress-percentage {
  font-weight: 600;
  color: var(--el-color-primary);
  font-size: 12px;
}

.progress-bar {
  margin: 4px 0;
}

.progress-message {
  text-align: center;
}

.time-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 11px;
}

.time-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.error-icon {
  color: var(--el-color-danger);
  cursor: pointer;
}

.error-icon:hover {
  color: var(--el-color-danger-light-3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .email-check-progress {
    min-width: 150px;
    padding: 6px;
  }
  
  .time-info {
    font-size: 10px;
  }
  
  .progress-percentage {
    font-size: 11px;
  }
}
</style>
