#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def check_schema():
    """检查数据库表结构"""
    try:
        conn = sqlite3.connect('data/huohuo_email.db')
        cursor = conn.cursor()
        
        # 查看mail_records表结构
        cursor.execute('PRAGMA table_info(mail_records)')
        columns = cursor.fetchall()
        
        print('mail_records表结构:')
        print('-' * 50)
        for col in columns:
            cid, name, type_, notnull, default, pk = col
            nullable = "NOT NULL" if notnull else "NULL"
            primary = "PRIMARY KEY" if pk else ""
            print(f"{name:20} {type_:15} {nullable:10} {primary}")
        
        # 查看索引
        cursor.execute("SELECT name, sql FROM sqlite_master WHERE type='index' AND tbl_name='mail_records'")
        indexes = cursor.fetchall()
        
        print('\nmail_records表索引:')
        print('-' * 50)
        for idx in indexes:
            print(f"索引名: {idx[0]}")
            print(f"SQL: {idx[1]}")
            print()
        
        conn.close()
        
    except Exception as e:
        print(f"检查表结构失败: {e}")

if __name__ == "__main__":
    check_schema()
