# 花火邮箱助手 系统架构

## 架构概述

花火邮箱助手采用前后端分离的架构设计，主要包含以下几个核心部分：

1. **前端界面**：基于Vue.js和Element Plus构建的用户界面
2. **后端API服务**：使用Flask框架实现的RESTful API
3. **WebSocket服务**：用于实时通信和进度反馈
4. **数据库**：SQLite关系型数据库用于数据存储
5. **邮件处理引擎**：处理邮件收取、解析和存储的核心模块

## 系统架构图

```
+-------------------+        +-------------------+
|                   |        |                   |
|   用户浏览器       |  HTTP  |   前端应用        |
|   (Browser)       |<------>|   (Vue.js)        |
|                   |        |                   |
+-------------------+        +---------^---------+
                                      |
                                      | WebSocket/HTTP
                                      |
+-------------------+        +---------v---------+
|                   |        |                   |
|   数据库          |<------>|   后端API服务      |
|   (SQLite)        |        |   (Flask)         |
|                   |        |                   |
+-------------------+        +---------^---------+
                                      |
                                      | 调用
                                      |
                             +---------v---------+
                             |                   |
                             |   邮件处理引擎     |
                             |   (Python)        |
                             |                   |
                             +-------------------+
                                      |
                                      | OAuth/IMAP
                                      |
                             +---------v---------+
                             |                   |
                             |   邮件服务器       |
                             |   (Outlook等)     |
                             |                   |
                             +-------------------+
```

## 核心组件说明

### 1. 前端应用

- **技术栈**：Vue.js 3, Vite, Element Plus
- **主要功能**：
  - 用户界面渲染
  - 表单验证
  - 数据展示
  - 与后端API通信
  - 通过WebSocket接收实时通知

### 2. 后端API服务

- **技术栈**：Python, Flask
- **主要功能**：
  - 用户认证和授权
  - 邮箱管理
  - 邮件管理
  - 数据库操作
  - 配置管理

### 3. WebSocket服务

- **技术栈**：Python, WebSockets
- **主要功能**：
  - 实时状态更新
  - 邮箱处理进度通知
  - 邮件收取状态反馈

### 4. 数据库

- **技术栈**：SQLite
- **主要功能**：
  - 用户数据存储
  - 邮箱信息存储
  - 邮件记录存储
  - 系统配置存储

### 5. 邮件处理引擎

- **技术栈**：Python, IMAP, OAuth2
- **主要功能**：
  - 连接邮箱服务器
  - 验证邮箱凭据
  - 检索和下载邮件
  - 解析邮件内容
  - 处理附件

## 数据流

1. **用户操作**：用户通过前端界面发起请求
2. **前端处理**：Vue.js应用处理用户输入并发送API请求
3. **后端处理**：Flask API接收请求，进行验证后执行相应操作
4. **数据存储**：将操作结果存储到SQLite数据库
5. **实时反馈**：通过WebSocket将处理进度或结果实时推送给前端
6. **用户反馈**：前端接收到WebSocket消息后更新界面，向用户展示结果

## 部署架构

该系统支持两种部署方式：

### 本地部署

直接在用户的本地服务器或个人电脑上运行，适合个人使用或小规模团队。

### Docker容器部署

将整个应用打包为Docker镜像，便于在各种环境中快速部署和迁移。

```
+-------------------+
|                   |
|   Docker容器       |
|                   |
|  +-----------+    |
|  |  Nginx    |    |
|  +-----------+    |
|        |          |
|  +-----------+    |
|  |  前端     |    |
|  +-----------+    |
|        |          |
|  +-----------+    |
|  |  后端API  |    |
|  +-----------+    |
|        |          |
|  +-----------+    |
|  | WebSocket |    |
|  +-----------+    |
|        |          |
|  +-----------+    |
|  | SQLite DB |    |
|  +-----------+    |
|                   |
+-------------------+
```

## 安全架构

1. **JWT认证**：使用JSON Web Token进行用户身份验证
2. **密码加密**：使用PBKDF2和SHA-256算法对密码进行加密存储
3. **数据隔离**：确保用户只能访问自己的邮箱和邮件数据
4. **权限控制**：管理员和普通用户权限分离
5. **本地存储**：敏感数据存储在本地SQLite数据库，不依赖外部服务 