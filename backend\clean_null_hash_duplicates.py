#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import hashlib
import json

def generate_mail_hash(subject, sender, received_time, content_preview=""):
    """生成邮件的唯一哈希标识"""
    # 标准化时间格式
    if hasattr(received_time, 'strftime'):
        time_str = received_time.strftime("%Y-%m-%d %H:%M:%S")
    else:
        time_str = str(received_time)
    
    # 创建哈希输入字符串
    hash_input = f"{subject or ''}{sender or ''}{time_str}{content_preview}"
    
    # 生成SHA256哈希
    return hashlib.sha256(hash_input.encode('utf-8')).hexdigest()

def clean_null_hash_duplicates():
    """清理哈希值为NULL的重复记录"""
    try:
        conn = sqlite3.connect('data/huohuo_email.db')
        cursor = conn.cursor()
        
        print("开始清理哈希值为NULL的重复记录...")
        
        # 获取所有哈希值为NULL的记录
        cursor.execute('''
            SELECT id, subject, sender, received_time, content 
            FROM mail_records 
            WHERE mail_hash IS NULL 
            ORDER BY id
        ''')
        
        null_hash_records = cursor.fetchall()
        print(f"发现 {len(null_hash_records)} 条哈希值为NULL的记录")
        
        deleted_count = 0
        
        for record in null_hash_records:
            record_id, subject, sender, received_time, content = record
            
            # 生成这条记录的哈希值
            content_preview = ""
            if content:
                if isinstance(content, str) and content.startswith('{'):
                    try:
                        parsed_content = json.loads(content)
                        content_preview = str(parsed_content.get('content', ''))[:100]
                    except:
                        content_preview = content[:100]
                else:
                    content_preview = str(content)[:100]
            
            mail_hash = generate_mail_hash(subject, sender, received_time, content_preview)
            
            # 检查这个哈希值是否已经存在
            cursor.execute('SELECT id FROM mail_records WHERE mail_hash = ? AND id != ?', (mail_hash, record_id))
            existing_record = cursor.fetchone()
            
            if existing_record:
                # 如果哈希值已存在，说明这是重复记录，删除它
                print(f"删除重复记录 ID: {record_id}, 主题: {subject[:40]}...")
                cursor.execute('DELETE FROM mail_records WHERE id = ?', (record_id,))
                deleted_count += 1
            else:
                # 如果哈希值不存在，更新这条记录的哈希值
                print(f"更新记录哈希值 ID: {record_id}, 主题: {subject[:40]}...")
                cursor.execute('UPDATE mail_records SET mail_hash = ? WHERE id = ?', (mail_hash, record_id))
        
        # 提交事务
        conn.commit()
        print(f"清理完成，删除了 {deleted_count} 条重复记录")
        
        # 验证结果
        cursor.execute('SELECT COUNT(*) FROM mail_records WHERE mail_hash IS NULL')
        remaining_null = cursor.fetchone()[0]
        print(f"剩余哈希值为NULL的记录数: {remaining_null}")
        
        # 检查是否还有重复记录
        cursor.execute('''
            SELECT subject, sender, received_time, COUNT(*) as count 
            FROM mail_records 
            GROUP BY subject, sender, received_time 
            HAVING COUNT(*) > 1
        ''')
        
        duplicates = cursor.fetchall()
        print(f"剩余重复记录组数: {len(duplicates)}")
        
        conn.close()
        
    except Exception as e:
        print(f"清理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    clean_null_hash_duplicates()
