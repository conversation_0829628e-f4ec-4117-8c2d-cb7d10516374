import { defineStore } from 'pinia';
import api from '@/services/api';
import websocket from '@/services/websocket';

export const useEmailsStore = defineStore('emails', {
  state: () => ({
    emails: [],
    loading: false,
    error: null,
    selectedEmails: [],
    processingEmails: {},
    currentMailRecords: [],
    currentEmailId: null,
    isConnected: false,
    // 增强的状态管理
    emailStatuses: {}, // 邮箱状态详细信息
    lastUpdateTime: null, // 最后更新时间
    connectionRetries: 0, // 连接重试次数
    maxRetries: 3 // 最大重试次数
  }),

  getters: {
    getEmailById: (state) => (id) => {
      return state.emails.find(email => email.id === id);
    },

    getProcessingStatus: (state) => (id) => {
      return state.processingEmails[id] || null;
    },

    hasSelectedEmails: (state) => {
      return Array.isArray(state.selectedEmails) && state.selectedEmails.length > 0;
    },

    selectedEmailsCount: (state) => {
      return Array.isArray(state.selectedEmails) ? state.selectedEmails.length : 0;
    },

    isAllSelected: (state) => {
      return state.emails.length > 0 && Array.isArray(state.selectedEmails) &&
        state.selectedEmails.length === state.emails.length;
    },

    // 增强的getters
    getEmailStatus: (state) => (emailId) => {
      return state.emailStatuses[emailId] || {
        status: 'idle',
        progress: 0,
        message: '',
        error: false,
        errorMessage: '',
        startTime: null,
        endTime: null,
        lastActivity: null
      };
    },

    getProcessingEmailsCount: (state) => {
      return Object.keys(state.processingEmails).length;
    },

    isAnyEmailProcessing: (state) => {
      return Object.values(state.processingEmails).some(status =>
        status && status.progress >= 0 && status.progress < 100
      );
    },

    getConnectionStatus: (state) => {
      return {
        isConnected: state.isConnected,
        retries: state.connectionRetries,
        maxRetries: state.maxRetries,
        lastUpdate: state.lastUpdateTime
      };
    }
  },

  actions: {
    // 初始化WebSocket事件监听
    initWebSocketListeners() {
      // 连接状态
      websocket.onConnect(() => {
        this.isConnected = true;
        this.connectionRetries = 0; // 重置重试次数
        this.lastUpdateTime = new Date().toISOString();
        this.fetchEmails();
      });

      websocket.onDisconnect(() => {
        this.isConnected = false;
        this.connectionRetries++;
        this.lastUpdateTime = new Date().toISOString();

        // 如果超过最大重试次数，清理所有处理状态
        if (this.connectionRetries >= this.maxRetries) {
          this.clearAllProcessingStates();
        }
      });

      // 邮箱列表更新
      websocket.onMessage('emails_list', (data) => {
        console.log('接收到邮箱列表：', data);
        if (data && Array.isArray(data.data)) {
          this.emails = data.data || [];
        }
      });

      // 新增邮箱
      websocket.onMessage('email_added', (data) => {
        console.log('邮箱添加成功：', data);
        this.fetchEmails();
      });

      // 删除邮箱
      websocket.onMessage('emails_deleted', (data) => {
        if (data.email_ids) {
          this.emails = this.emails.filter(email => !data.email_ids.includes(email.id));
          // 更新已选邮箱
          this.selectedEmails = this.selectedEmails.filter(id => !data.email_ids.includes(id));
        }
      });

      // 邮箱导入
      websocket.onMessage('emails_imported', () => {
        this.fetchEmails();
      });

      // 处理进度更新
      websocket.onMessage('check_progress', (data) => {
        const { email_id, progress, message, timestamp } = data;
        const currentTime = new Date().toISOString();

        // 更新处理状态
        this.processingEmails[email_id] = {
          progress,
          message,
          timestamp: timestamp || currentTime,
          lastUpdate: currentTime
        };

        // 更新详细状态
        if (!this.emailStatuses[email_id]) {
          this.emailStatuses[email_id] = {
            status: 'processing',
            progress: 0,
            message: '',
            error: false,
            errorMessage: '',
            startTime: currentTime,
            endTime: null,
            lastActivity: currentTime
          };
        }

        // 更新状态信息
        this.emailStatuses[email_id] = {
          ...this.emailStatuses[email_id],
          progress,
          message,
          lastActivity: currentTime,
          status: progress === 100 ? 'completed' : 'processing'
        };

        // 如果是开始状态，记录开始时间
        if (progress === 0 && !this.emailStatuses[email_id].startTime) {
          this.emailStatuses[email_id].startTime = currentTime;
        }

        // 进度完成后的处理
        if (progress === 100) {
          // 使用新的完成方法
          this.completeEmailCheck(email_id, message);

          // 延迟刷新邮箱列表，确保服务器已完成处理
          setTimeout(() => {
            this.fetchEmails();
            // 如果正在查看此邮箱的邮件，也刷新邮件列表
            if (this.currentEmailId === email_id) {
              this.fetchMailRecords(email_id);
            }
          }, 500);
        }

        // 更新最后更新时间
        this.lastUpdateTime = currentTime;
      });

      // 邮件记录
      websocket.onMessage('mail_records', (data) => {
        if (data.email_id === this.currentEmailId) {
          // 添加数据验证和清理
          if (Array.isArray(data.data)) {
            // 确保每条记录都有必要的字段
            this.currentMailRecords = data.data.map(record => ({
              id: record.id || Date.now() + Math.random().toString(36).substring(2, 10),
              subject: record.subject || '(无主题)',
              sender: record.sender || '(未知发件人)',
              received_time: record.received_time || new Date().toISOString(),
              content: record.content || '(无内容)',
              folder: record.folder || 'INBOX'
            }));
          } else {
            this.currentMailRecords = [];
            console.error('收到的邮件记录数据不是数组格式:', data);
          }
        }
      });

      // 错误处理
      websocket.onMessage('error', (data) => {
        this.error = data.message;
        console.error('WebSocket 错误：', data.message);
      });
    },

    // 添加邮箱
    async addEmail(emailData) {
      this.loading = true;
      this.error = null;

      try {
        console.log('添加邮箱：', {...emailData, password: '******'});
        if (!websocket.isConnected) {
          await api.emails.add(emailData);
        } else {
          // 确保mail_type参数正确传递
          const wsData = {
            ...emailData,
            mail_type: emailData.mail_type || 'imap' // 默认使用imap类型
          };
          websocket.send('add_email', wsData);
        }
      } catch (error) {
        this.error = '添加邮箱失败';
        throw error;
      } finally {
        this.loading = false;
      }
    },

    // 导入邮箱
    async importEmails(importData) {
      this.loading = true;
      this.error = null;

      try {
        console.log('导入邮箱：', importData);
        if (!websocket.isConnected) {
          // 确保传递完整的导入数据对象，包括mail_type
          await api.emails.import(importData);
        } else {
          websocket.send('import_emails', importData);
        }
      } catch (error) {
        this.error = '导入邮箱失败';
        throw error;
      } finally {
        this.loading = false;
      }
    },

    // 获取所有邮箱
    async fetchEmails() {
      this.loading = true;
      this.error = null;

      try {
        console.log('获取邮箱列表，WebSocket状态：', websocket.isConnected);
        if (!websocket.isConnected) {
          const response = await api.emails.getAll();
          this.emails = response;
        } else {
          websocket.send('get_all_emails');
        }
      } catch (error) {
        this.error = '获取邮箱列表失败';
        console.error(error);
      } finally {
        this.loading = false;
      }
    },

    // 删除单个邮箱
    async deleteEmail(emailId) {
      this.loading = true;
      this.error = null;

      try {
        if (!websocket.isConnected) {
          await api.emails.delete([emailId]);
        } else {
          websocket.send('delete_emails', { email_ids: [emailId] });
        }

        // 更新本地状态
        this.emails = this.emails.filter(email => email.id !== emailId);
        if (Array.isArray(this.selectedEmails)) {
          this.selectedEmails = this.selectedEmails.filter(id => id !== emailId);
        }
      } catch (error) {
        this.error = '删除邮箱失败';
        throw error;
      } finally {
        this.loading = false;
      }
    },

    // 批量删除邮箱
    async deleteEmails(emailIds) {
      if (!Array.isArray(emailIds) || emailIds.length === 0) {
        return;
      }

      this.loading = true;
      this.error = null;

      try {
        if (!websocket.isConnected) {
          await api.emails.delete(emailIds);
        } else {
          websocket.send('delete_emails', { email_ids: emailIds });
        }

        // 更新本地状态
        this.emails = this.emails.filter(email => !emailIds.includes(email.id));
        if (Array.isArray(this.selectedEmails)) {
          this.selectedEmails = this.selectedEmails.filter(id => !emailIds.includes(id));
        }
      } catch (error) {
        this.error = '删除邮箱失败';
        throw error;
      } finally {
        this.loading = false;
      }
    },

    // 检查单个邮箱
    async checkEmail(emailId) {
      try {
        // 优先使用WebSocket
        if (websocket.isConnected && websocket.isAuthenticated) {
          console.log(`通过WebSocket检查邮箱 ID:${emailId}`);
          websocket.send('check_emails', { email_ids: [emailId] });
          return { success: true, message: '已开始检查邮箱', method: 'websocket' };
        }

        // 使用API作为备选方案
        console.log(`通过API检查邮箱 ID:${emailId}`);

        // 设置开始状态
        this.updateEmailStatus(emailId, {
          status: 'processing',
          progress: 0,
          message: '开始检查邮箱...',
          startTime: new Date().toISOString()
        });

        const response = await api.emails.check([emailId]);

        // 处理响应
        if (response.status === 409) {
          // 邮箱正在处理中，这是正常状态，不抛出错误
          console.log('邮箱正在处理中:', response.data);
          return { success: false, message: response.data.message, status: 'processing' };
        }

        // 检查成功，显示结果
        const result = response.data;
        console.log('邮箱检查结果:', result);

        if (result.success) {
          // 设置完成状态
          this.completeEmailCheck(emailId, result.message);

          // 刷新邮箱列表
          setTimeout(() => {
            this.fetchEmails();
          }, 1000);

          return { success: true, message: result.message, method: 'api' };
        } else {
          // 设置错误状态
          this.setEmailError(emailId, result.message);
          return { success: false, message: result.message, method: 'api' };
        }

      } catch (error) {
        // 特殊处理409状态码（邮箱正在处理中）
        if (error.response && error.response.status === 409) {
          console.log('邮箱正在处理中:', error.response.data);
          return { success: false, message: error.response.data.message, status: 'processing' };
        }

        console.error('检查邮箱失败:', error);

        // 设置错误状态
        this.setEmailError(emailId, error.message || '检查邮箱失败');

        throw error;
      }
    },

    // 批量检查邮箱
    async checkEmails(emailIds) {
      if (!Array.isArray(emailIds) || emailIds.length === 0) {
        return;
      }

      this.loading = true;
      this.error = null;

      try {
        if (!websocket.isConnected) {
          await api.emails.check(emailIds);
        } else {
          websocket.send('check_emails', { email_ids: emailIds });
        }
      } catch (error) {
        this.error = '检查邮箱失败';
        throw error;
      } finally {
        this.loading = false;
      }
    },

    // 获取邮件记录
    async fetchMailRecords(emailId) {
      this.loading = true;
      this.error = null;

      try {
        if (!websocket.isConnected) {
          const response = await api.emails.getRecords(emailId);

          // 确保返回数据是数组且每条记录格式正确
          if (Array.isArray(response)) {
            this.currentMailRecords = response.map(record => ({
              id: record.id || Date.now() + Math.random().toString(36).substring(2, 10),
              subject: record.subject || '(无主题)',
              sender: record.sender || '(未知发件人)',
              received_time: record.received_time || new Date().toISOString(),
              content: record.content || '(无内容)',
              folder: record.folder || 'INBOX'
            }));
          } else {
            this.currentMailRecords = [];
            console.error('API返回的邮件记录数据不是数组格式:', response);
          }

          this.currentEmailId = emailId;
        } else {
          this.currentEmailId = emailId;
          websocket.send('get_mail_records', { email_id: emailId });
        }
      } catch (error) {
        this.error = '获取邮件记录失败';
        console.error(error);
      } finally {
        this.loading = false;
      }
    },

    // 获取邮箱密码
    async getEmailPassword(emailId) {
      try {
        return await api.emails.getPassword(emailId);
      } catch (error) {
        this.error = '获取邮箱密码失败';
        throw error;
      }
    },

    // 选择/取消选择邮箱
    toggleSelectEmail(emailId) {
      if (!Array.isArray(this.selectedEmails)) {
        this.selectedEmails = [];
      }

      const index = this.selectedEmails.indexOf(emailId);
      if (index === -1) {
        this.selectedEmails.push(emailId);
      } else {
        this.selectedEmails.splice(index, 1);
      }
    },

    // 选择所有邮箱
    selectAllEmails() {
      if (!Array.isArray(this.emails)) {
        this.selectedEmails = [];
        return;
      }
      this.selectedEmails = this.emails.map(email => email.id);
    },

    // 重置状态
    resetState() {
      this.emails = [];
      this.loading = false;
      this.error = null;
      this.selectedEmails = [];
      this.processingEmails = {};
      this.currentMailRecords = [];
      this.currentEmailId = null;
      this.isConnected = false;
    },

    // 更新邮箱
    async updateEmail(email) {
      try {
        // 确保IMAP类型邮箱的use_ssl是布尔值
        const emailData = { ...email }
        if (emailData.mail_type === 'imap' && 'use_ssl' in emailData) {
          emailData.use_ssl = Boolean(emailData.use_ssl)
        }

        // 使用api对象调用，确保使用正确的基础URL
        console.log(`更新邮箱 ID:${emailData.id}`);
        const response = await api.put(`/emails/${emailData.id}`, emailData);

        // 更新本地邮箱数据
        const index = this.emails.findIndex(e => e.id === emailData.id);
        if (index !== -1) {
          this.emails[index] = { ...this.emails[index], ...emailData };
        }

        return true;
      } catch (error) {
        console.error('更新邮箱失败:', error);
        throw error;
      }
    },

    // 增强的状态管理方法
    setEmailError(emailId, errorMessage) {
      if (!this.emailStatuses[emailId]) {
        this.emailStatuses[emailId] = {
          status: 'error',
          progress: 0,
          message: '',
          error: true,
          errorMessage,
          startTime: null,
          endTime: null,
          lastActivity: new Date().toISOString()
        };
      } else {
        this.emailStatuses[emailId] = {
          ...this.emailStatuses[emailId],
          status: 'error',
          error: true,
          errorMessage,
          lastActivity: new Date().toISOString()
        };
      }
    },

    clearEmailError(emailId) {
      if (this.emailStatuses[emailId]) {
        this.emailStatuses[emailId] = {
          ...this.emailStatuses[emailId],
          error: false,
          errorMessage: '',
          status: 'idle'
        };
      }
    },

    clearAllProcessingStates() {
      this.processingEmails = {};
      // 将所有处理中的状态重置为空闲
      Object.keys(this.emailStatuses).forEach(emailId => {
        if (this.emailStatuses[emailId].status === 'processing') {
          this.emailStatuses[emailId] = {
            ...this.emailStatuses[emailId],
            status: 'idle',
            progress: 0,
            message: '',
            endTime: new Date().toISOString()
          };
        }
      });
    },

    updateEmailStatus(emailId, statusUpdate) {
      if (!this.emailStatuses[emailId]) {
        this.emailStatuses[emailId] = {
          status: 'idle',
          progress: 0,
          message: '',
          error: false,
          errorMessage: '',
          startTime: null,
          endTime: null,
          lastActivity: new Date().toISOString()
        };
      }

      this.emailStatuses[emailId] = {
        ...this.emailStatuses[emailId],
        ...statusUpdate,
        lastActivity: new Date().toISOString()
      };
    },

    // 完成邮箱检查
    completeEmailCheck(emailId, message = '') {
      const currentTime = new Date().toISOString();

      // 更新状态为完成
      this.updateEmailStatus(emailId, {
        status: 'completed',
        progress: 100,
        message: message || '检查完成',
        endTime: currentTime,
        error: false,
        errorMessage: ''
      });

      // 清理处理状态
      delete this.processingEmails[emailId];

      // 5秒后重置为空闲状态，但保持进度100%显示一段时间
      setTimeout(() => {
        if (this.emailStatuses[emailId] && this.emailStatuses[emailId].status === 'completed') {
          this.updateEmailStatus(emailId, {
            status: 'idle',
            progress: 100, // 保持100%进度
            message: message || '检查完成' // 保持完成消息
          });

          // 再过3秒后完全重置
          setTimeout(() => {
            if (this.emailStatuses[emailId]) {
              this.updateEmailStatus(emailId, {
                status: 'idle',
                progress: 0,
                message: ''
              });
            }
          }, 3000);
        }
      }, 5000);
    }
  }
});