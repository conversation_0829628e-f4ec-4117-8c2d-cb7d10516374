# 前端组件设计

## 组件架构概述

花火邮箱助手前端采用组件化开发方式，将UI界面拆分为可复用的组件。组件设计遵循以下原则：

1. **单一职责**：每个组件专注于一个功能点
2. **可复用性**：组件设计成可复用的模块
3. **松耦合**：组件之间尽量减少依赖
4. **易测试**：组件易于单元测试

## 核心组件详解

### 1. App.vue

根组件，负责整体布局和全局状态管理。

**功能特点**：
- 全局布局结构定义
- WebSocket连接初始化
- 全局导航栏
- 用户认证状态监听
- 路由视图展示

**核心代码片段**：
```vue
<template>
  <el-config-provider :locale="zhCn">
    <div class="app-container">
      <el-container>
        <el-header class="app-header" :class="{ 'scrolled': isScrolled }">
          <!-- 头部导航栏 -->
        </el-header>
        
        <!-- 导航菜单 -->
        <el-menu v-if="isAuthenticated" mode="horizontal" :router="true" :default-active="$route.path" class="app-nav">
          <!-- 菜单项 -->
        </el-menu>
        
        <el-main>
          <router-view v-slot="{ Component }" v-if="!initializing">
            <transition name="fade" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
        </el-main>
        
        <el-footer class="app-footer">
          <!-- 页脚 -->
        </el-footer>
      </el-container>
      
      <!-- 通知组件 -->
      <Notifications />
    </div>
  </el-config-provider>
</template>
```

### 2. EmailsView.vue

邮箱管理视图组件，负责展示和管理邮箱列表。

**功能特点**：
- 邮箱列表展示
- 批量操作工具栏
- 分页功能
- 筛选和搜索功能
- 邮箱状态标记

**实现细节**：
- 使用ElTable组件展示邮箱数据
- 通过WebSocket接收邮箱状态更新
- 使用Vuex进行状态管理
- 支持多选和批量操作

### 3. AddEmailForm.vue

添加邮箱表单组件，负责新邮箱的输入和验证。

**功能特点**：
- 表单输入验证
- 多种邮箱类型支持
- 动态表单字段
- 错误提示

**实现细节**：
- 使用ElForm组件进行表单管理
- 实现表单验证规则
- 根据邮箱类型显示不同的表单字段

### 4. MailRecords.vue

邮件记录视图组件，负责展示特定邮箱的邮件列表。

**功能特点**：
- 邮件列表展示
- 邮件详情查看
- 邮件内容渲染
- 附件展示和下载

**实现细节**：
- 使用ElTable展示邮件列表
- 使用ElDialog展示邮件详情
- 支持HTML内容安全渲染
- 实现邮件分页和筛选

### 5. Notifications.vue

通知组件，负责显示系统通知和处理进度。

**功能特点**：
- 实时进度条展示
- 操作成功/失败通知
- 系统消息通知
- 通知堆叠管理

**实现细节**：
- 使用ElNotification组件显示通知
- 监听WebSocket消息更新通知
- 通过Vuex管理通知状态
- 支持通知关闭和交互

### 6. UserManagement.vue

用户管理组件，仅对管理员可见，负责用户账户管理。

**功能特点**：
- 用户列表展示
- 添加/删除用户
- 重置用户密码
- 用户权限管理

**实现细节**：
- 使用ElTable展示用户列表
- 通过Dialog实现用户添加和编辑
- 实现权限控制和安全检查

### 7. SearchView.vue

邮件搜索视图，负责全局邮件搜索功能。

**功能特点**：
- 高级搜索条件设置
- 搜索结果展示
- 搜索历史记录
- 结果排序和筛选

**实现细节**：
- 使用ElForm实现搜索条件表单
- 通过ElTable展示搜索结果
- 支持多种搜索条件组合
- 实现搜索结果分页

## 通用组件

### 1. LoadingIndicator.vue

加载指示器组件，用于显示数据加载状态。

```vue
<template>
  <div class="loading-container" v-if="loading">
    <el-skeleton :rows="rows" animated />
  </div>
</template>

<script setup>
defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  rows: {
    type: Number,
    default: 5
  }
});
</script>
```

### 2. ConfirmDialog.vue

确认对话框组件，用于显示操作确认提示。

```vue
<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    width="30%"
    @close="handleClose"
  >
    <span>{{ message }}</span>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>
```

### 3. StatusTag.vue

状态标签组件，用于显示邮箱或操作状态。

```vue
<template>
  <el-tag :type="tagType" :effect="effect">{{ text }}</el-tag>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  status: {
    type: String,
    required: true
  },
  effect: {
    type: String,
    default: 'light'
  }
});

const tagType = computed(() => {
  switch (props.status) {
    case 'success': return 'success';
    case 'processing': return 'warning';
    case 'error': return 'danger';
    default: return 'info';
  }
});

const text = computed(() => {
  switch (props.status) {
    case 'success': return '成功';
    case 'processing': return '处理中';
    case 'error': return '失败';
    default: return props.status;
  }
});
</script>
```

### 4. EmptyState.vue

空状态组件，用于显示无数据时的提示。

```vue
<template>
  <div class="empty-container">
    <el-empty :description="description" :image-size="100">
      <template #default>
        <slot>
          <el-button type="primary" @click="$emit('action')">{{ actionText }}</el-button>
        </slot>
      </template>
    </el-empty>
  </div>
</template>
```

## 组件通信方式

### Props和Events

父子组件之间的基本通信方式：

```vue
<!-- 父组件 -->
<ChildComponent :data="parentData" @update="handleUpdate" />

<!-- 子组件 -->
<script setup>
const props = defineProps(['data']);
const emit = defineEmits(['update']);

function updateData(newValue) {
  emit('update', newValue);
}
</script>
```

### Vuex状态管理

跨组件通信和状态共享：

```javascript
// 组件中使用Vuex
import { useStore } from 'vuex';
import { computed } from 'vue';

const store = useStore();
const emails = computed(() => store.state.emails.list);

// 触发actions
function loadEmails() {
  store.dispatch('emails/fetchEmails');
}
```

### Provide/Inject

深层组件嵌套中的状态共享：

```javascript
// 父组件提供数据
provide('themeColor', ref('dark'));

// 子组件注入数据
const themeColor = inject('themeColor');
```

## 组件样式设计

组件样式采用以下策略：

1. **基础样式**：使用Element Plus提供的基础样式
2. **自定义主题**：通过CSS变量定制Element Plus主题
3. **组件局部样式**：使用`scoped`属性限制样式作用域
4. **全局样式**：在`App.vue`中定义全局样式变量和主题

**主题定制示例**：
```css
:root {
  --primary-color: #3B82F6;
  --success-color: #22C55E;
  --warning-color: #F59E0B;
  --danger-color: #EF4444;
  --text-color: #333333;
  --background-color: #F8FAFC;
}

/* 深色模式 */
.dark-theme {
  --text-color: #F1F5F9;
  --background-color: #1E293B;
}
``` 