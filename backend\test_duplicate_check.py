#!/usr/bin/env python3
"""
测试邮件重复检查逻辑
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime
from utils.email.common import generate_mail_hash, get_china_now
from database.db import Database

def test_mail_hash():
    """测试邮件哈希生成"""
    print("=== 测试邮件哈希生成 ===")
    
    # 测试相同邮件生成相同哈希
    subject = "测试邮件主题"
    sender = "<EMAIL>"
    received_time = get_china_now()
    content = "这是一封测试邮件的内容"
    
    hash1 = generate_mail_hash(subject, sender, received_time, content)
    hash2 = generate_mail_hash(subject, sender, received_time, content)
    
    print(f"哈希1: {hash1}")
    print(f"哈希2: {hash2}")
    print(f"哈希相同: {hash1 == hash2}")
    
    # 测试不同邮件生成不同哈希
    hash3 = generate_mail_hash(subject + "不同", sender, received_time, content)
    print(f"哈希3: {hash3}")
    print(f"哈希1与哈希3不同: {hash1 != hash3}")
    
    print()

def test_database_duplicate_check():
    """测试数据库重复检查"""
    print("=== 测试数据库重复检查 ===")
    
    try:
        db = Database()
        
        # 测试数据
        email_id = 1
        subject = "测试重复检查邮件"
        sender = "<EMAIL>"
        received_time = get_china_now()
        content = {"content": "这是一封用于测试重复检查的邮件"}
        
        print(f"测试邮件: {subject}")
        print(f"发件人: {sender}")
        print(f"时间: {received_time}")
        
        # 第一次添加
        success1, mail_id1 = db.add_mail_record(
            email_id=email_id,
            subject=subject,
            sender=sender,
            received_time=received_time,
            content=content,
            folder="TEST"
        )
        
        print(f"第一次添加: 成功={success1}, 邮件ID={mail_id1}")
        
        # 第二次添加相同邮件（应该被拒绝）
        success2, mail_id2 = db.add_mail_record(
            email_id=email_id,
            subject=subject,
            sender=sender,
            received_time=received_time,
            content=content,
            folder="TEST"
        )
        
        print(f"第二次添加: 成功={success2}, 邮件ID={mail_id2}")
        
        if success1 and not success2:
            print("✅ 重复检查工作正常！")
        else:
            print("❌ 重复检查可能有问题！")
            
        # 清理测试数据
        if mail_id1:
            db.conn.execute("DELETE FROM mail_records WHERE id = ?", (mail_id1,))
            db.conn.commit()
            print("已清理测试数据")
            
    except Exception as e:
        print(f"测试失败: {str(e)}")
    
    print()

def main():
    """主测试函数"""
    print("开始测试邮件重复检查逻辑...")
    print()
    
    test_mail_hash()
    test_database_duplicate_check()
    
    print("测试完成！")

if __name__ == "__main__":
    main()
