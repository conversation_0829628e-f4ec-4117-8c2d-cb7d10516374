@import './base.css';

#app {
  width: 100%;
  min-height: 100vh;
  margin: 0 auto;
  font-weight: normal;
}

.card {
  padding: 20px;
  border-radius: var(--border-radius-md);
  box-shadow: var(--box-shadow);
  background-color: white;
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.card:hover {
  box-shadow: var(--box-shadow-md);
}

.page-container {
  padding: 20px;
  max-width: 1280px;
  margin: 0 auto;
}

.flex {
  display: flex;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-grow {
  flex-grow: 1;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.gap-sm {
  gap: 0.5rem;
}

.gap-md {
  gap: 1rem;
}

.gap-lg {
  gap: 1.5rem;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.full-width {
  width: 100%;
}

.m-0 { margin: 0; }
.m-1 { margin: 0.25rem; }
.m-2 { margin: 0.5rem; }
.m-3 { margin: 0.75rem; }
.m-4 { margin: 1rem; }
.m-5 { margin: 1.25rem; }
.m-6 { margin: 1.5rem; }
.m-8 { margin: 2rem; }

.mx-auto { margin-left: auto; margin-right: auto; }
.my-auto { margin-top: auto; margin-bottom: auto; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }
.mt-6 { margin-top: 1.5rem; }
.mt-8 { margin-top: 2rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mb-20 { margin-bottom: 20px; }

.p-0 { padding: 0; }
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-5 { padding: 1.25rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }

.app-title {
  color: var(--primary-color);
  font-weight: bold;
  font-size: 24px;
  margin-bottom: 20px;
  position: relative;
  display: inline-block;
}

.app-title::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: var(--primary-color);
  border-radius: var(--border-radius-full);
}

.text-primary { color: var(--primary-color); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-danger { color: var(--danger-color); }
.text-info { color: var(--info-color); }
.text-muted { color: var(--secondary-text-color); }

.bg-primary { background-color: var(--primary-color); }
.bg-success { background-color: var(--success-color); }
.bg-warning { background-color: var(--warning-color); }
.bg-danger { background-color: var(--danger-color); }
.bg-info { background-color: var(--info-color); }
.bg-light { background-color: var(--background-color-light); }
.bg-dark { background-color: var(--background-color-dark); }

.cursor-pointer {
  cursor: pointer;
}

.transition {
  transition: all var(--transition-normal);
}

.hover-scale {
  transition: transform var(--transition-normal);
}

.hover-scale:hover {
  transform: scale(1.02);
}

.shadow-sm { box-shadow: var(--box-shadow-sm); }
.shadow { box-shadow: var(--box-shadow); }
.shadow-md { box-shadow: var(--box-shadow-md); }
.shadow-lg { box-shadow: var(--box-shadow-lg); }

.rounded-sm { border-radius: var(--border-radius-sm); }
.rounded { border-radius: var(--border-radius); }
.rounded-md { border-radius: var(--border-radius-md); }
.rounded-lg { border-radius: var(--border-radius-lg); }
.rounded-full { border-radius: var(--border-radius-full); }

@media (max-width: 768px) {
  .sm-hidden { display: none; }
  .sm-flex-col { flex-direction: column; }
  .sm-w-full { width: 100%; }
  .sm-text-center { text-align: center; }
  .sm-mt-4 { margin-top: 1rem; }
  .sm-p-2 { padding: 0.5rem; }
}

@media (max-width: 480px) {
  .xs-hidden { display: none; }
  .xs-flex-col { flex-direction: column; }
  .xs-w-full { width: 100%; }
  .xs-text-center { text-align: center; }
  .xs-mt-2 { margin-top: 0.5rem; }
  .xs-p-1 { padding: 0.25rem; }
} 