#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import hashlib
import json
from datetime import datetime

def generate_mail_hash(subject, sender, received_time, content_preview=""):
    """生成邮件的唯一哈希标识"""
    # 标准化时间格式
    if hasattr(received_time, 'strftime'):
        time_str = received_time.strftime("%Y-%m-%d %H:%M:%S")
    else:
        time_str = str(received_time)
    
    # 创建哈希输入字符串
    hash_input = f"{subject or ''}{sender or ''}{time_str}{content_preview}"
    
    # 生成SHA256哈希
    return hashlib.sha256(hash_input.encode('utf-8')).hexdigest()

def migrate_database():
    """迁移数据库，添加邮件哈希字段和唯一索引"""
    try:
        conn = sqlite3.connect('data/huohuo_email.db')
        cursor = conn.cursor()
        
        print("开始数据库迁移...")
        
        # 1. 检查是否已经有mail_hash字段
        cursor.execute('PRAGMA table_info(mail_records)')
        columns = [col[1] for col in cursor.fetchall()]
        
        if 'mail_hash' not in columns:
            print("添加mail_hash字段...")
            cursor.execute('ALTER TABLE mail_records ADD COLUMN mail_hash TEXT')
            conn.commit()
        else:
            print("mail_hash字段已存在")
        
        # 2. 为现有记录生成哈希值
        print("为现有记录生成哈希值...")
        cursor.execute('SELECT id, subject, sender, received_time, content FROM mail_records WHERE mail_hash IS NULL')
        records = cursor.fetchall()
        
        updated_count = 0
        for record in records:
            record_id, subject, sender, received_time, content = record
            
            # 获取内容预览
            content_preview = ""
            if content:
                if isinstance(content, str) and content.startswith('{'):
                    try:
                        parsed_content = json.loads(content)
                        content_preview = str(parsed_content.get('content', ''))[:100]
                    except:
                        content_preview = content[:100]
                else:
                    content_preview = str(content)[:100]
            
            # 生成哈希
            mail_hash = generate_mail_hash(subject, sender, received_time, content_preview)
            
            # 更新记录
            cursor.execute('UPDATE mail_records SET mail_hash = ? WHERE id = ?', (mail_hash, record_id))
            updated_count += 1
            
            if updated_count % 10 == 0:
                print(f"已处理 {updated_count}/{len(records)} 条记录")
        
        conn.commit()
        print(f"完成哈希值生成，共更新 {updated_count} 条记录")
        
        # 3. 创建唯一索引（先检查是否存在）
        cursor.execute("SELECT name FROM sqlite_master WHERE type='index' AND name='idx_mail_hash_unique'")
        if not cursor.fetchone():
            print("创建邮件哈希唯一索引...")
            cursor.execute('CREATE UNIQUE INDEX idx_mail_hash_unique ON mail_records(mail_hash)')
            conn.commit()
            print("唯一索引创建成功")
        else:
            print("唯一索引已存在")
        
        # 4. 创建复合索引用于快速重复检查
        cursor.execute("SELECT name FROM sqlite_master WHERE type='index' AND name='idx_mail_duplicate_check'")
        if not cursor.fetchone():
            print("创建重复检查复合索引...")
            cursor.execute('CREATE INDEX idx_mail_duplicate_check ON mail_records(email_id, subject, sender, received_time)')
            conn.commit()
            print("复合索引创建成功")
        else:
            print("复合索引已存在")
        
        print("数据库迁移完成！")
        
        # 5. 验证迁移结果
        cursor.execute('SELECT COUNT(*) FROM mail_records WHERE mail_hash IS NOT NULL')
        hash_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM mail_records')
        total_count = cursor.fetchone()[0]
        
        print(f"验证结果: {hash_count}/{total_count} 条记录有哈希值")
        
        conn.close()
        
    except Exception as e:
        print(f"数据库迁移失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    migrate_database()
