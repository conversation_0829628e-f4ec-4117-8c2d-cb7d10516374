#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import sys

def check_duplicates():
    """检查数据库中的重复邮件"""
    try:
        conn = sqlite3.connect('data/huohuo_email.db')
        cursor = conn.cursor()
        
        # 查找重复邮件
        cursor.execute('''
            SELECT subject, sender, received_time, COUNT(*) as count 
            FROM mail_records 
            GROUP BY subject, sender, received_time 
            HAVING COUNT(*) > 1 
            ORDER BY count DESC
        ''')
        
        duplicates = cursor.fetchall()
        
        print(f"发现 {len(duplicates)} 组重复邮件:")
        print("-" * 80)
        
        total_extra = 0
        for i, (subject, sender, received_time, count) in enumerate(duplicates, 1):
            print(f"{i}. 主题: {subject[:50]}...")
            print(f"   发件人: {sender[:40]}...")
            print(f"   时间: {received_time}")
            print(f"   重复次数: {count}")
            print()
            total_extra += (count - 1)
        
        print(f"总计多余的重复记录数: {total_extra}")
        
        # 查看具体的重复记录ID
        if duplicates:
            print("\n详细的重复记录ID:")
            for subject, sender, received_time, count in duplicates:
                cursor.execute('''
                    SELECT id FROM mail_records 
                    WHERE subject = ? AND sender = ? AND received_time = ?
                    ORDER BY id
                ''', (subject, sender, received_time))
                
                ids = [row[0] for row in cursor.fetchall()]
                print(f"主题: {subject[:30]}... -> IDs: {ids}")
        
        conn.close()
        
    except Exception as e:
        print(f"检查重复邮件失败: {e}")

if __name__ == "__main__":
    check_duplicates()
