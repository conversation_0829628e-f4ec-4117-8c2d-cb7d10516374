#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from database.db import Database
from datetime import datetime, timezone
import json

def test_duplicate_prevention():
    """测试重复邮件防护功能"""
    print("=== 测试重复邮件防护功能 ===")
    
    try:
        # 初始化数据库
        db = Database()
        
        # 测试数据
        email_id = 1  # 假设存在的邮箱ID
        subject = "测试重复邮件防护"
        sender = "<EMAIL>"
        received_time = datetime.now(timezone.utc)
        content = {
            "content": "这是一封测试邮件的内容，用于验证重复检查功能",
            "content_type": "text/plain",
            "has_html": False
        }
        
        print(f"测试邮件信息:")
        print(f"  邮箱ID: {email_id}")
        print(f"  主题: {subject}")
        print(f"  发件人: {sender}")
        print(f"  时间: {received_time}")
        print()
        
        # 第一次添加邮件
        print("第一次添加邮件...")
        success1, mail_id1 = db.add_mail_record(
            email_id=email_id,
            subject=subject,
            sender=sender,
            received_time=received_time,
            content=content,
            folder="INBOX"
        )
        
        print(f"结果: 成功={success1}, 邮件ID={mail_id1}")
        
        if success1:
            print("✅ 第一次添加成功")
        else:
            print("❌ 第一次添加失败")
            return
        
        print()
        
        # 第二次添加相同邮件（应该被拒绝）
        print("第二次添加相同邮件...")
        success2, mail_id2 = db.add_mail_record(
            email_id=email_id,
            subject=subject,
            sender=sender,
            received_time=received_time,
            content=content,
            folder="INBOX"
        )
        
        print(f"结果: 成功={success2}, 邮件ID={mail_id2}")
        
        if not success2:
            print("✅ 第二次添加被正确拒绝（重复检查生效）")
        else:
            print("❌ 第二次添加成功（重复检查失效）")
            return
        
        print()
        
        # 第三次添加稍微不同的邮件（应该成功）
        print("第三次添加稍微不同的邮件...")
        different_content = {
            "content": "这是一封不同的测试邮件内容",
            "content_type": "text/plain",
            "has_html": False
        }
        
        success3, mail_id3 = db.add_mail_record(
            email_id=email_id,
            subject=subject + " - 不同版本",
            sender=sender,
            received_time=received_time,
            content=different_content,
            folder="INBOX"
        )
        
        print(f"结果: 成功={success3}, 邮件ID={mail_id3}")
        
        if success3:
            print("✅ 第三次添加成功（内容不同，应该允许）")
        else:
            print("❌ 第三次添加失败（内容不同，应该允许）")
        
        print()
        
        # 清理测试数据
        print("清理测试数据...")
        if mail_id1:
            db.conn.execute("DELETE FROM mail_records WHERE id = ?", (mail_id1,))
        if mail_id3:
            db.conn.execute("DELETE FROM mail_records WHERE id = ?", (mail_id3,))
        db.conn.commit()
        print("✅ 测试数据清理完成")
        
        print()
        print("🎉 重复邮件防护功能测试通过！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if 'db' in locals():
            db.close()

def test_hash_generation():
    """测试哈希生成的一致性"""
    print("=== 测试哈希生成一致性 ===")
    
    try:
        from utils.email.common import generate_mail_hash
        
        # 测试数据
        subject = "测试邮件主题"
        sender = "<EMAIL>"
        received_time = datetime.now(timezone.utc)
        content = "这是邮件内容"
        
        # 生成多次哈希，应该相同
        hash1 = generate_mail_hash(subject, sender, received_time, content)
        hash2 = generate_mail_hash(subject, sender, received_time, content)
        hash3 = generate_mail_hash(subject, sender, received_time, content)
        
        print(f"哈希1: {hash1}")
        print(f"哈希2: {hash2}")
        print(f"哈希3: {hash3}")
        
        if hash1 == hash2 == hash3:
            print("✅ 哈希生成一致性测试通过")
        else:
            print("❌ 哈希生成一致性测试失败")
            return
        
        # 测试不同内容生成不同哈希
        hash4 = generate_mail_hash(subject + "不同", sender, received_time, content)
        
        if hash1 != hash4:
            print("✅ 不同内容生成不同哈希测试通过")
        else:
            print("❌ 不同内容生成不同哈希测试失败")
        
        print()
        
    except Exception as e:
        print(f"❌ 哈希生成测试失败: {e}")

if __name__ == "__main__":
    test_hash_generation()
    print()
    test_duplicate_prevention()
