#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def check_specific_duplicates():
    """检查特定的重复邮件记录"""
    try:
        conn = sqlite3.connect('data/huohuo_email.db')
        cursor = conn.cursor()
        
        # 检查Canceled邮件
        print("检查Canceled邮件记录:")
        cursor.execute('''
            SELECT id, subject, sender, received_time, mail_hash 
            FROM mail_records 
            WHERE subject LIKE 'Canceled: delivery from order%' 
            ORDER BY id
        ''')
        
        records = cursor.fetchall()
        for record in records:
            record_id, subject, sender, received_time, mail_hash = record
            hash_preview = mail_hash[:16] + "..." if mail_hash else "NULL"
            print(f"ID: {record_id}, 主题: {subject[:40]}..., 哈希: {hash_preview}")
        
        print("\n" + "="*60)
        
        # 检查Nino邮件
        print("检查Nino邮件记录:")
        cursor.execute('''
            SELECT id, subject, sender, received_time, mail_hash 
            FROM mail_records 
            WHERE subject LIKE 'Nino, thanks for your order%' 
            ORDER BY id
        ''')
        
        records = cursor.fetchall()
        for record in records:
            record_id, subject, sender, received_time, mail_hash = record
            hash_preview = mail_hash[:16] + "..." if mail_hash else "NULL"
            print(f"ID: {record_id}, 主题: {subject[:40]}..., 哈希: {hash_preview}")
        
        print("\n" + "="*60)
        
        # 检查是否有NULL哈希值的记录
        cursor.execute('SELECT COUNT(*) FROM mail_records WHERE mail_hash IS NULL')
        null_hash_count = cursor.fetchone()[0]
        print(f"哈希值为NULL的记录数: {null_hash_count}")
        
        if null_hash_count > 0:
            print("哈希值为NULL的记录:")
            cursor.execute('''
                SELECT id, subject, sender, received_time 
                FROM mail_records 
                WHERE mail_hash IS NULL 
                ORDER BY id DESC 
                LIMIT 10
            ''')
            
            records = cursor.fetchall()
            for record in records:
                record_id, subject, sender, received_time = record
                print(f"ID: {record_id}, 主题: {subject[:40] if subject else 'NULL'}..., 时间: {received_time}")
        
        conn.close()
        
    except Exception as e:
        print(f"检查失败: {e}")

if __name__ == "__main__":
    check_specific_duplicates()
