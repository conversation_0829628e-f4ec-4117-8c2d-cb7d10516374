"""
邮件处理模块
将Outlook和IMAP处理逻辑分离到不同的文件中
"""

from .common import (
    decode_mime_words,
    strip_html,
    safe_decode,
    remove_extra_blank_lines,
    parse_email_date,
    decode_email_content,
)
from .outlook import OutlookMailHandler
from .imap import IMAPMailHandler
from .gmail import <PERSON><PERSON><PERSON><PERSON><PERSON>
from .qq import QQMailHandler
from .yahoo import YahooMailHandler
from .mail_processor import MailProcessor, EmailBatchProcessor
from .file_parser import EmailFileParser

# 保持原有API兼容性
__all__ = [
    'decode_mime_words',
    'strip_html',
    'safe_decode',
    'remove_extra_blank_lines',
    'parse_email_date',
    'decode_email_content',
    'OutlookMailHandler',
    'IMAPMailHandler',
    'GmailHandler',
    'QQMailHandler',
    'YahooMailHandler',
    'MailProcessor',
    'EmailBatchProcessor',
    'EmailFileParser',
]