#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def remove_duplicate_records():
    """删除重复的邮件记录，保留最早的记录"""
    try:
        conn = sqlite3.connect('data/huohuo_email.db')
        cursor = conn.cursor()
        
        print("开始删除重复邮件记录...")
        
        # 1. 查找重复的哈希值
        cursor.execute('''
            SELECT mail_hash, COUNT(*) as count 
            FROM mail_records 
            WHERE mail_hash IS NOT NULL
            GROUP BY mail_hash 
            HAVING COUNT(*) > 1
            ORDER BY count DESC
        ''')
        
        duplicate_hashes = cursor.fetchall()
        print(f"发现 {len(duplicate_hashes)} 组重复哈希值")
        
        total_deleted = 0
        
        for mail_hash, count in duplicate_hashes:
            print(f"处理哈希值 {mail_hash[:16]}... (重复 {count} 次)")
            
            # 获取所有具有相同哈希值的记录，按ID排序
            cursor.execute('''
                SELECT id, subject, sender, received_time 
                FROM mail_records 
                WHERE mail_hash = ? 
                ORDER BY id ASC
            ''', (mail_hash,))
            
            records = cursor.fetchall()
            
            if len(records) > 1:
                # 保留第一条记录（ID最小的），删除其他的
                keep_id = records[0][0]
                delete_ids = [record[0] for record in records[1:]]
                
                print(f"  保留记录ID: {keep_id}")
                print(f"  删除记录ID: {delete_ids}")
                print(f"  主题: {records[0][1][:50]}...")
                
                # 删除重复记录
                for delete_id in delete_ids:
                    cursor.execute('DELETE FROM mail_records WHERE id = ?', (delete_id,))
                    total_deleted += 1
                
                print(f"  已删除 {len(delete_ids)} 条重复记录")
                print()
        
        # 提交事务
        conn.commit()
        print(f"删除重复记录完成，总计删除 {total_deleted} 条记录")
        
        # 2. 验证删除结果
        cursor.execute('''
            SELECT mail_hash, COUNT(*) as count 
            FROM mail_records 
            WHERE mail_hash IS NOT NULL
            GROUP BY mail_hash 
            HAVING COUNT(*) > 1
        ''')
        
        remaining_duplicates = cursor.fetchall()
        print(f"剩余重复记录组数: {len(remaining_duplicates)}")
        
        # 3. 现在尝试创建唯一索引
        try:
            cursor.execute("SELECT name FROM sqlite_master WHERE type='index' AND name='idx_mail_hash_unique'")
            if not cursor.fetchone():
                print("创建邮件哈希唯一索引...")
                cursor.execute('CREATE UNIQUE INDEX idx_mail_hash_unique ON mail_records(mail_hash)')
                conn.commit()
                print("唯一索引创建成功")
            else:
                print("唯一索引已存在")
        except Exception as e:
            print(f"创建唯一索引失败: {e}")
        
        # 4. 创建复合索引用于快速重复检查
        try:
            cursor.execute("SELECT name FROM sqlite_master WHERE type='index' AND name='idx_mail_duplicate_check'")
            if not cursor.fetchone():
                print("创建重复检查复合索引...")
                cursor.execute('CREATE INDEX idx_mail_duplicate_check ON mail_records(email_id, subject, sender, received_time)')
                conn.commit()
                print("复合索引创建成功")
            else:
                print("复合索引已存在")
        except Exception as e:
            print(f"创建复合索引失败: {e}")
        
        conn.close()
        print("重复记录清理完成！")
        
    except Exception as e:
        print(f"删除重复记录失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    remove_duplicate_records()
